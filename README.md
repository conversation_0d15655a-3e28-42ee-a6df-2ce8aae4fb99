# Reddit爬虫系统

这是一个功能完整的Reddit内容爬取与存储系统，可以自动抓取指定子版块的帖子和评论，并将数据存储到PostgreSQL数据库中。

## 功能特性

* **多种抓取模式**：支持热门(hot)、最新(new)、最佳(top)、上升中(rising)和有争议(controversial)等多种排序方式的内容抓取
* **完整数据存储**：结构化保存子版块、帖子及评论数据
* **定时任务**：支持配置定时抓取任务
* **数据库存储**：使用PostgreSQL数据库，支持外键约束和索引优化
* **灵活配置**：通过配置文件自定义爬虫行为
* **详细日志**：提供完整的日志记录功能

## 环境要求

* Python 3.7+
* PostgreSQL 12+
* 依赖包：
  - praw (Reddit API包)
  - psycopg2-binary (PostgreSQL连接器)
  - schedule (定时任务)
  - python-dotenv (环境变量管理)
  - pyyaml (配置文件解析)

## 安装步骤

1. 克隆仓库到本地：
   ```bash
   git clone https://github.com/yourusername/spider-reddit.git
   cd spider-reddit
   ```

2. 创建并激活虚拟环境：
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # Linux/macOS
   # 或者
   .venv\Scripts\activate  # Windows
   ```

3. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

4. 配置环境变量（创建.env文件）：
   ```
   # 数据库配置
   DB_HOST=localhost
   DB_PORT=5432
   DB_USER=postgres
   DB_PASSWORD=yourpassword
   DB_NAME=reddit_data
   
   # Reddit API配置
   REDDIT_CLIENT_ID=your_client_id
   REDDIT_CLIENT_SECRET=your_client_secret
   ```

## 使用方法

### 立即执行爬虫

```bash
cd src
python main.py --now
```

### 启动定时任务

```bash
cd src
python main.py
```

系统将根据配置文件中设置的时间定时执行爬虫任务。

## 配置说明

配置文件位于`config/config.yaml`，支持以下配置项：

```yaml
paths:
  cookies_path: "data/cookies/cookies.json"
  raw_data: "data/raw"
  processed_data: "data/processed"
  logs: "logs/parser.log"

schedule:
  daily_time: "02:00"  # 每天执行时间

logging:
  config_file: "config/logging.yaml"

reddit:
  default_subreddit: "Taiwanese"  # 默认子版块
  posts_limit: 10  # 每次抓取帖子数量
  comments_limit: 5  # 每个帖子抓取评论数量
  rankings:  # 排序方式
    - hot
    - new
    - top
  time_filters:  # 时间过滤
    top: "week"
    controversial: "day"
```

## 项目结构

```
spider-reddit/
├── config/               # 配置文件目录
│   ├── config.yaml       # 主配置文件
│   └── logging.yaml      # 日志配置
├── data/                 # 数据目录
│   ├── raw/              # 原始数据
│   └── processed/        # 处理后数据
├── logs/                 # 日志目录
├── src/                  # 源代码
│   ├── database/         # 数据库相关
│   │   └── db_pool.py    # 数据库连接池
│   ├── dao/              # 数据访问对象
│   │   ├── comment_dao.py  # 评论DAO
│   │   ├── post_dao.py    # 帖子DAO
│   │   └── subreddit_dao.py # 子版块DAO
│   ├── spider/           # 爬虫模块
│   │   └── spider_reddit.py # Reddit爬虫
│   ├── utils/            # 工具函数
│   │   └── logger.py     # 日志工具
│   └── main.py           # 主程序
├── .env                  # 环境变量
├── requirements.txt      # 依赖包列表
└── README.md             # 说明文档
```

## 数据库结构

系统使用三个主要表来存储数据：

1. **reddit_subreddits** - 存储子版块信息
   - id (主键)
   - name (子版块名称，唯一)
   - display_name (显示名称)
   - subscribers (订阅人数)
   - public_description (公开描述)
   - created_utc (创建时间)
   - last_crawled_at (最后抓取时间)

2. **reddit_posts** - 存储帖子信息
   - id (主键)
   - post_id (Reddit帖子ID，唯一)
   - subreddit (所属子版块名称)
   - title (帖子标题)
   - author (作者用户名)
   - created_utc (发布时间)
   - score (评分/得分)
   - upvote_ratio (赞成率)
   - url (原始URL)
   - permalink (Reddit永久链接)
   - num_comments (评论数量)
   - is_self (是否为自发帖子)
   - selftext (帖子文本内容)
   - is_original_content (是否为原创内容)
   - created_at (记录创建时间)
   - updated_at (记录更新时间)

3. **reddit_comments** - 存储评论信息
   - id (主键)
   - comment_id (Reddit评论ID，唯一)
   - post_id (关联的帖子ID，外键)
   - author (评论作者)
   - body (评论内容)
   - score (评论得分)
   - created_utc (发布时间)
   - is_submitter (是否为原帖作者)
   - created_at (记录创建时间)
   - updated_at (记录更新时间)

## 维护与说明

- 如遇到`psycopg2`安装问题，请尝试使用`psycopg2-binary`替代
- 获取Reddit API凭证需要在[Reddit开发者网站](https://www.reddit.com/prefs/apps)注册
- 定期检查日志文件，避免磁盘空间占用过大


## 本地启动命令：
1. uv venv --python 3.12.10
2. source venv/bin/activate
3. uv pip install -r requirements.txt
4. uv run src/main.py --now
