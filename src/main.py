import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

import schedule
import time
import yaml
from dotenv import load_dotenv
from utils.logger import setup_logging, get_logger
from database.db_pool import DatabasePool

# 确保日志目录存在
log_dir = Path(project_root) / 'logs'
log_dir.mkdir(exist_ok=True)

# 设置日志
setup_logging()
logger = get_logger('main')

# 加载环境变量
load_dotenv()

# 导入Reddit爬虫函数
from spider.spider_reddit import (
    save_subreddit_info,
    crawl_and_save_by_ranking,
    get_subreddit_rank,
    format_and_print_by_ranking
)

# 导入DAO类
from dao.subreddit_dao import SubredditDao
from dao.post_dao import PostDao
from dao.comment_dao import CommentDao

# 配置参数
REDDIT_POSTS_LIMIT = 5
REDDIT_COMMENTS_LIMIT = 10  # 修改为10条评论

def load_config():
    """加载配置文件"""
    try:
        config_path = Path(project_root) / 'config' / 'config.yaml'
        if not config_path.exists():
            logger.warning(f"配置文件不存在: {config_path}")
            return create_default_config()
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 替换配置中的变量
        root_dir = project_root
        for section in config:
            if isinstance(config[section], dict):
                for key, value in config[section].items():
                    if isinstance(value, str) and '%(root_dir)s' in value:
                        config[section][key] = value.replace('%(root_dir)s', root_dir)
        
        # 验证配置是否包含必要的部分
        if not validate_config(config):
            logger.warning("配置文件缺少必要的配置项，将使用默认配置补充")
            config = merge_with_default_config(config)
            
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        return create_default_config()

def create_default_config():
    """创建默认配置"""
    return {
        'paths': {
            'cookies_path': "data/cookies/cookies.json",
            'raw_data': "data/raw",
            'processed_data': "data/processed",
            'logs': "logs/parser.log"
        },
        'schedule': {
            'daily_time': "02:00"
        },
        'logging': {
            'config_file': "config/logging.yaml"
        },
        'ENABLE_REDDIT': True,
        'REDDIT': {
            'SUBREDDITS': [
                'apple',
                'Entrepreneur',
                'startups',
                'smallbusiness',
                'EntrepreneurRideAlong',
                'InternetIsBeautiful',
                'SideProject',
                'SaaS',
                'indiehackers',
                'indiebiz',
                'IMadeThis',
                'alphaandbetausers',
                'roastmystartup',
                'AppBusiness',
                'AppHookup',
                'design_critiques',
                'iosapps',
                'copywriting',
                'advertising',
                'marketing',
                'digital_marketing',
                'socialmedia',
                'SocialMediaMarketing',
                'Instagram',
                'InstagramMarketing',
                'Blogging',
                'ProductivityApps',
                'productivity',
                'webdev',
                'GrowthHacking',
                'web_design',
                'writing',
                'Twitter',
                'apps',
                'IPhoneApps',
                'AppDevelopers',
                'Startup_Ideas'
            ],
            'posts_limit': 5,
            'comments_limit': 10,
            'rankings': ['hot', 'new', 'top'],
            'time_filters': {
                'top': 'week',
                'controversial': 'day'
            }
        }
    }

def validate_config(config):
    """验证配置是否包含必要的部分"""
    required_sections = ['paths', 'schedule', 'logging']
    for section in required_sections:
        if section not in config:
            return False
    
    # 验证数据库配置
    db_config = config.get('database', {})
    required_db_keys = ['host', 'port', 'user', 'password', 'database']
    for key in required_db_keys:
        if key not in db_config:
            return False
    
    return True

def merge_with_default_config(config):
    """将配置与默认配置合并"""
    default_config = create_default_config()
    
    # 合并顶级部分
    for section in default_config:
        if section not in config:
            config[section] = default_config[section]
        elif isinstance(default_config[section], dict):
            # 合并子部分
            for key in default_config[section]:
                if key not in config[section]:
                    config[section][key] = default_config[section][key]
    
    return config

# 加载配置
CONFIG = load_config()

# Notion配置
NOTION_CONFIG = CONFIG.get('notion', {})
NOTION_API_KEY = NOTION_CONFIG.get('api_key', os.getenv('NOTION_API_KEY', ''))
NOTION_DATABASE_ID = NOTION_CONFIG.get('database_id', os.getenv('NOTION_DATABASE_ID', ''))

def verify_environment_variables():
    """验证必要的环境变量是否已设置"""
    required_vars = {
        'DB_HOST': '数据库主机',
        'DB_PORT': '数据库端口',
        'DB_USER': '数据库用户名',
        'DB_PASSWORD': '数据库密码',
        'DB_NAME': '数据库名称',
        'REDDIT_CLIENT_ID': 'Reddit客户端ID',
        'REDDIT_CLIENT_SECRET': 'Reddit客户端密钥'
    }
    
    missing_vars = []
    for var, desc in required_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({desc})")
    
    if missing_vars:
        logger.error("缺少以下必要的环境变量:")
        for var in missing_vars:
            logger.error(f"  - {var}")
        return False
    
    return True

def setup_database():
    """初始化数据库连接和表结构"""
    try:
        db_pool = DatabasePool()
        conn = db_pool.get_connection()
        
        # PostgreSQL不允许在事务中创建数据库，需要先关闭自动提交
        conn.autocommit = True
        cursor = conn.cursor()
        
        # 检查数据库是否存在
        db_name = os.getenv('DB_NAME')
        cursor.execute("""
            SELECT 1 FROM pg_database WHERE datname = %s
        """, (db_name,))
        
        # 如果数据库不存在则创建
        if not cursor.fetchone():
            cursor.execute(f'CREATE DATABASE {db_name}')
            logger.info(f"数据库 {db_name} 创建成功")
        
        # 关闭当前连接
        cursor.close()
        conn.close()
        
        # 重新连接到新创建的数据库
        conn = db_pool.get_connection()
        cursor = conn.cursor()
        
        # 创建Reddit相关表，分开执行每条语句，确保错误不会影响整个过程
        try:
            # 创建Reddit帖子表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS reddit_posts (
                id SERIAL PRIMARY KEY, -- 自增主键
                post_id VARCHAR(20) NOT NULL UNIQUE, -- Reddit帖子ID，唯一
                subreddit VARCHAR(100) NOT NULL, -- 所属子版块名称
                title VARCHAR(300) NOT NULL, -- 帖子标题
                author VARCHAR(100), -- 作者用户名
                created_utc TIMESTAMP, -- 发布时间
                score INTEGER DEFAULT 0, -- 评分/得分
                upvote_ratio FLOAT, -- 赞成率
                url TEXT, -- 原始URL
                permalink TEXT, -- Reddit永久链接
                num_comments INTEGER DEFAULT 0, -- 评论数量
                is_self BOOLEAN DEFAULT FALSE, -- 是否为自发帖子
                selftext TEXT, -- 帖子文本内容
                is_original_content BOOLEAN DEFAULT FALSE, -- 是否为原创内容
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 记录更新时间
            )
            """)
            
            logger.info("Reddit帖子表创建成功")
            
            # 创建帖子表索引
            try:
                cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_post_subreddit ON reddit_posts(subreddit);
                CREATE INDEX IF NOT EXISTS idx_post_author ON reddit_posts(author);
                CREATE INDEX IF NOT EXISTS idx_post_created ON reddit_posts(created_utc);
                CREATE INDEX IF NOT EXISTS idx_post_score ON reddit_posts(score)
                """)
                
                logger.info("Reddit帖子表索引创建成功")
            except Exception as e:
                logger.warning(f"创建帖子表索引失败，但将继续: {e}")
            
            # 添加帖子表注释
            try:
                cursor.execute("""
                COMMENT ON TABLE reddit_posts IS 'Reddit帖子信息表';
                COMMENT ON COLUMN reddit_posts.id IS '主键ID';
                COMMENT ON COLUMN reddit_posts.post_id IS 'Reddit帖子ID';
                COMMENT ON COLUMN reddit_posts.subreddit IS '所属子版块名称';
                COMMENT ON COLUMN reddit_posts.title IS '帖子标题';
                COMMENT ON COLUMN reddit_posts.author IS '作者用户名';
                COMMENT ON COLUMN reddit_posts.created_utc IS '发布时间';
                COMMENT ON COLUMN reddit_posts.score IS '评分/得分';
                COMMENT ON COLUMN reddit_posts.upvote_ratio IS '赞成率';
                COMMENT ON COLUMN reddit_posts.url IS '原始URL';
                COMMENT ON COLUMN reddit_posts.permalink IS 'Reddit永久链接';
                COMMENT ON COLUMN reddit_posts.num_comments IS '评论数量';
                COMMENT ON COLUMN reddit_posts.is_self IS '是否为自发帖子';
                COMMENT ON COLUMN reddit_posts.selftext IS '帖子文本内容';
                COMMENT ON COLUMN reddit_posts.is_original_content IS '是否为原创内容';
                COMMENT ON COLUMN reddit_posts.created_at IS '记录创建时间';
                COMMENT ON COLUMN reddit_posts.updated_at IS '记录更新时间'
                """)
                
                logger.info("Reddit帖子表注释添加成功")
            except Exception as e:
                logger.warning(f"添加帖子表注释失败，但将继续: {e}")
                
        except Exception as e:
            logger.warning(f"创建Reddit帖子表失败，但将继续: {e}")
        
        try:
            # 创建Reddit评论表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS reddit_comments (
                id SERIAL PRIMARY KEY, -- 自增主键
                comment_id VARCHAR(20) NOT NULL UNIQUE, -- Reddit评论ID，唯一
                post_id VARCHAR(20) NOT NULL, -- 关联的帖子ID
                author VARCHAR(100), -- 评论作者
                body TEXT, -- 评论内容
                score INTEGER DEFAULT 0, -- 评论得分
                created_utc TIMESTAMP, -- 发布时间
                is_submitter BOOLEAN DEFAULT FALSE, -- 是否为原帖作者
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 记录更新时间
            )
            """)
            
            logger.info("Reddit评论表创建成功")
            
            # 稍后添加外键约束
            try:
                cursor.execute("""
                ALTER TABLE reddit_comments
                ADD CONSTRAINT fk_comment_post 
                FOREIGN KEY (post_id) REFERENCES reddit_posts(post_id) ON DELETE CASCADE
                """)
                
                logger.info("Reddit评论表外键约束添加成功")
            except Exception as e:
                logger.warning(f"添加评论表外键约束失败，但将继续: {e}")
            
            # 创建评论表索引
            try:
                cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_comment_post_id ON reddit_comments(post_id);
                CREATE INDEX IF NOT EXISTS idx_comment_author ON reddit_comments(author);
                CREATE INDEX IF NOT EXISTS idx_comment_created ON reddit_comments(created_utc);
                CREATE INDEX IF NOT EXISTS idx_comment_score ON reddit_comments(score)
                """)
                
                logger.info("Reddit评论表索引创建成功")
            except Exception as e:
                logger.warning(f"创建评论表索引失败，但将继续: {e}")
            
            # 添加评论表注释
            try:
                cursor.execute("""
                COMMENT ON TABLE reddit_comments IS 'Reddit评论信息表';
                COMMENT ON COLUMN reddit_comments.id IS '主键ID';
                COMMENT ON COLUMN reddit_comments.comment_id IS 'Reddit评论ID';
                COMMENT ON COLUMN reddit_comments.post_id IS '关联的帖子ID';
                COMMENT ON COLUMN reddit_comments.author IS '评论作者';
                COMMENT ON COLUMN reddit_comments.body IS '评论内容';
                COMMENT ON COLUMN reddit_comments.score IS '评论得分';
                COMMENT ON COLUMN reddit_comments.created_utc IS '发布时间';
                COMMENT ON COLUMN reddit_comments.is_submitter IS '是否为原帖作者';
                COMMENT ON COLUMN reddit_comments.created_at IS '记录创建时间';
                COMMENT ON COLUMN reddit_comments.updated_at IS '记录更新时间'
                """)
                
                logger.info("Reddit评论表注释添加成功")
            except Exception as e:
                logger.warning(f"添加评论表注释失败，但将继续: {e}")
                
        except Exception as e:
            logger.warning(f"创建Reddit评论表失败，但将继续: {e}")
            
        try:
            # 创建触发器函数
            cursor.execute("""
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ language 'plpgsql'
            """)
            
            logger.info("更新时间触发器函数创建成功")
            
            # 为帖子表添加触发器
            try:
                cursor.execute("""
                DROP TRIGGER IF EXISTS update_reddit_posts_updated_at ON reddit_posts;
                CREATE TRIGGER update_reddit_posts_updated_at
                    BEFORE UPDATE ON reddit_posts
                    FOR EACH ROW
                    EXECUTE FUNCTION update_updated_at_column()
                """)
                
                logger.info("帖子表更新时间触发器创建成功")
            except Exception as e:
                logger.warning(f"创建帖子表触发器失败，但将继续: {e}")
            
            # 为评论表添加触发器
            try:
                cursor.execute("""
                DROP TRIGGER IF EXISTS update_reddit_comments_updated_at ON reddit_comments;
                CREATE TRIGGER update_reddit_comments_updated_at
                    BEFORE UPDATE ON reddit_comments
                    FOR EACH ROW
                    EXECUTE FUNCTION update_updated_at_column()
                """)
                
                logger.info("评论表更新时间触发器创建成功")
            except Exception as e:
                logger.warning(f"创建评论表触发器失败，但将继续: {e}")
                
        except Exception as e:
            logger.warning(f"创建更新时间触发器失败，但将继续: {e}")
        
        # 我们不在这里创建子版块表，而是依赖SubredditDao类来创建
        
        conn.commit()
        logger.info("数据库初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        return False
    finally:
        if 'conn' in locals() and 'cursor' in locals():
            DatabasePool.close_connection(conn, cursor)

def setup_logging_from_config():
    """从配置文件设置日志"""
    try:
        # 获取项目根目录
        root_dir = Path(project_root)
        
        # 确保日志目录存在
        log_dir = root_dir / 'logs'
        log_dir.mkdir(exist_ok=True)
        
        # 加载日志配置文件
        logging_config_path = root_dir / 'config' / 'logging.yaml'
        if logging_config_path.exists():
            with open(logging_config_path, 'r', encoding='utf-8') as f:
                log_config = yaml.safe_load(f)
            
            # 替换配置中的路径变量
            if 'handlers' in log_config:
                for handler_config in log_config['handlers'].values():
                    if 'filename' in handler_config:
                        # 将相对路径转换为绝对路径
                        filename = handler_config['filename']
                        if not os.path.isabs(filename):
                            handler_config['filename'] = str(root_dir / filename)
                        
                        # 确保日志文件的目录存在
                        os.makedirs(os.path.dirname(handler_config['filename']), exist_ok=True)
            
            # 配置日志
            import logging.config
            logging.config.dictConfig(log_config)
            logging.info("日志配置已从配置文件加载")
        else:
            # 使用默认日志设置
            setup_logging()
            logging.warning(f"日志配置文件不存在: {logging_config_path}")
    except Exception as e:
        # 使用备选日志设置
        setup_logging()
        logging.error(f"加载日志配置失败: {str(e)}")

def run_spider():
    """执行爬虫任务"""
    try:
        logger.info("开始执行爬虫任务")
        
        # 获取并执行Reddit爬虫任务
        if CONFIG["ENABLE_REDDIT"]:
            logger.info("正在启动Reddit爬虫")
            # 确保数据表已创建
            SubredditDao.create_table_if_not_exists()
            PostDao.create_table_if_not_exists()
            CommentDao.create_table_if_not_exists()
            
            # 获取要抓取的子版块
            all_subreddits = CONFIG["REDDIT"]["SUBREDDITS"]
            batch_size = 5  # 每批处理5个子版块
            
            # 分批处理子版块
            total_batches = (len(all_subreddits) + batch_size - 1) // batch_size
            logger.info(f"共有 {len(all_subreddits)} 个子版块，将分 {total_batches} 批处理")
            
            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, len(all_subreddits))
                batch_subreddits = all_subreddits[start_idx:end_idx]
                
                logger.info(f"开始处理第 {batch_idx + 1}/{total_batches} 批子版块")
                
                for subreddit_name in batch_subreddits:
                    try:
                        logger.info(f"正在抓取子版块: {subreddit_name}")
                        
                        # 首先保存子版块信息（包括排名和URL）
                        save_subreddit_info(subreddit_name)
                        
                        # 抓取热门帖子
                        hot_result = crawl_and_save_by_ranking(
                            subreddit_name, 
                            "hot", 
                            REDDIT_POSTS_LIMIT, 
                            REDDIT_COMMENTS_LIMIT
                        )
                        
                        # 抓取最新帖子
                        new_result = crawl_and_save_by_ranking(
                            subreddit_name, 
                            "new", 
                            REDDIT_POSTS_LIMIT, 
                            REDDIT_COMMENTS_LIMIT
                        )
                        
                        # 抓取本周最佳帖子
                        top_result = crawl_and_save_by_ranking(
                            subreddit_name, 
                            "top", 
                            REDDIT_POSTS_LIMIT, 
                            REDDIT_COMMENTS_LIMIT, 
                            "week"
                        )
                        
                        logger.info(f"子版块 {subreddit_name} 抓取完成:")
                        logger.info(f"- 热门帖子: 已保存 {hot_result['posts_saved']} 条")
                        logger.info(f"- 最新帖子: 已保存 {new_result['posts_saved']} 条")
                        logger.info(f"- 本周最佳: 已保存 {top_result['posts_saved']} 条")
                        
                        # 每个子版块处理完后暂停3秒，避免请求过于频繁
                        time.sleep(3)
                        
                    except Exception as e:
                        logger.error(f"抓取子版块 {subreddit_name} 时出错: {str(e)}")
                
                # 每批处理完后暂停10秒
                if batch_idx < total_batches - 1:
                    logger.info(f"第 {batch_idx + 1} 批处理完成，暂停10秒后继续下一批")
                    time.sleep(10)
            
            logger.info("所有子版块抓取完成")
            
        return True
    except Exception as e:
        logger.error(f"Reddit爬虫执行出错: {str(e)}")
        return False

def schedule_tasks():
    """设置定时任务"""
    # 从配置文件获取定时设置，默认每天凌晨2点
    schedule_config = CONFIG.get('schedule', {})
    schedule_time = schedule_config.get('daily_time', "01:00")
    
    # 设置定时任务
    schedule.every().day.at(schedule_time).do(run_spider)
    
    logger.info(f"定时任务已设置，将在每天 {schedule_time} 执行Reddit爬虫")
    
    while True:
        schedule.run_pending()
        time.sleep(60)  # 每分钟检查一次是否有待执行的任务

def ensure_data_directories():
    """确保数据目录存在"""
    data_dirs = ['data', 'logs', 'data/raw', 'data/processed']
    for dir_name in data_dirs:
        os.makedirs(dir_name, exist_ok=True)
        logger.info(f"确保目录存在: {dir_name}")

def initialize_dao_tables():
    """初始化DAO相关的数据表"""
    try:
        # 创建数据表
        subreddit_result = SubredditDao.create_table_if_not_exists()
        post_result = PostDao.create_table_if_not_exists()
        comment_result = CommentDao.create_table_if_not_exists()
        
        if subreddit_result and post_result and comment_result:
            logger.info("所有Reddit数据表初始化成功")
            return True
        else:
            # 尝试采用错误恢复策略
            logger.warning("部分Reddit数据表初始化失败，尝试修复或自适应处理")
            
            # 如果子版块表创建失败，可能是因为缺少新增字段，尝试更新表结构
            if not subreddit_result:
                try:
                    logger.info("尝试手动修复子版块表结构")
                    conn = DatabasePool().get_connection()
                    cursor = conn.cursor()
                    
                    # 检查是否缺少rank列
                    cursor.execute("""
                    SELECT column_name FROM information_schema.columns 
                    WHERE table_name = 'reddit_subreddits' AND column_name = 'rank'
                    """)
                    
                    # 如果缺少rank列，添加它
                    if not cursor.fetchone():
                        logger.info("添加缺失的rank列")
                        cursor.execute("ALTER TABLE reddit_subreddits ADD COLUMN rank INTEGER")
                        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sub_rank ON reddit_subreddits(rank)")
                        cursor.execute("COMMENT ON COLUMN reddit_subreddits.rank IS '子版块排名'")
                    
                    # 检查是否缺少url列
                    cursor.execute("""
                    SELECT column_name FROM information_schema.columns 
                    WHERE table_name = 'reddit_subreddits' AND column_name = 'url'
                    """)
                    
                    # 如果缺少url列，添加它
                    if not cursor.fetchone():
                        logger.info("添加缺失的url列")
                        cursor.execute("ALTER TABLE reddit_subreddits ADD COLUMN url TEXT")
                        cursor.execute("COMMENT ON COLUMN reddit_subreddits.url IS '子版块URL地址'")
                    
                    conn.commit()
                    cursor.close()
                    conn.close()
                    
                    logger.info("子版块表结构修复完成")
                    return True
                except Exception as e:
                    logger.error(f"修复子版块表结构失败: {str(e)}")
                    return False
            
            return False
    except Exception as e:
        logger.error(f"初始化DAO数据表失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("Reddit爬虫程序启动")
    
    # 验证环境变量
    if not verify_environment_variables():
        logger.error("环境变量配置不完整，程序退出")
        sys.exit(1)
    
    # 确保数据目录存在
    ensure_data_directories()
    
    # 检查配置是否加载成功
    if CONFIG is None:
        logger.error("配置加载失败，程序退出")
        sys.exit(1)
    
    # 初始化数据库
    if not setup_database():
        logger.error("数据库初始化失败，程序退出")
        sys.exit(1)
    
    # 初始化DAO数据表
    if not initialize_dao_tables():
        logger.error("DAO数据表初始化失败，程序退出")
        sys.exit(1)
    
    # 解析命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--now":
        # 立即执行一次爬虫
        run_spider()
    else:
        # 启动定时任务
        schedule_tasks()
        # run_spider()

    logger.info("程序结束")

if __name__ == "__main__":
    main()
