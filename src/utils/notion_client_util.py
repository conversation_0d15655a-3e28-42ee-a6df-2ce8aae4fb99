"""
Notion integration module for AI Daily Report Generator.
"""
import logging
import json
import os
from typing import Dict, List
from datetime import datetime
from notion_client import Client
from markdown_it import MarkdownIt
from markdown_it.token import Token
import time
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

class NotionSync:
    """Sync reports to Notion workspace."""
    
    def __init__(self, config: Dict):
        """Initialize the Notion client with configuration."""
        self.config = config
        self.notion_json_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                           'data', 'notion.json')
        
        # 记录配置信息
        logger.info(f"初始化Notion客户端，配置: {config}")
        
        # Get API key from config
        api_key = config.get('api_key')
        if not api_key:
            logger.error("Notion API密钥未在配置中找到")
            raise ValueError("Notion API key not found in configuration")
            
        self.client = Client(auth=api_key)
        self.database_id = config.get('database_id')
        
        if not self.database_id:
            logger.error("Notion数据库ID未在配置中找到")
            raise ValueError("Notion database ID not found in configuration")
        
        logger.info(f"Notion客户端初始化成功，数据库ID: {self.database_id}")

    def _load_synced_articles(self) -> List[str]:
        """Load previously synced article IDs from JSON file."""
        try:
            if os.path.exists(self.notion_json_path):
                with open(self.notion_json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('article_ids', [])
            return []
        except Exception as e:
            logger.error(f"Error loading synced articles: {str(e)}")
            return []

    def _save_synced_articles(self, article_ids: List[str]):
        """Save synced article IDs to JSON file."""
        try:
            os.makedirs(os.path.dirname(self.notion_json_path), exist_ok=True)
            existing_ids = self._load_synced_articles()
            all_ids = list(set(existing_ids + article_ids))
            
            with open(self.notion_json_path, 'w', encoding='utf-8') as f:
                json.dump({'article_ids': all_ids}, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Error saving synced articles: {str(e)}")

    def sync_report(self, report: Dict) -> str:
        """
        Sync report to Notion database.
        
        Args:
            report: Report to sync
            
        Returns:
            str: URL to the Notion page
        """
        try:
            logger.info(f"Starting to sync report with {len(report.get('articles', []))} articles")
            
            # Load existing synced article IDs
            synced_ids = self._load_synced_articles()
            logger.info(f"Loaded {len(synced_ids)} existing synced article IDs")
            
            # Filter out already synced articles
            new_articles = [
                article for article in report.get('articles', [])
                if article['id'] not in synced_ids
            ]
            
            if not new_articles:
                logger.info("No new articles to sync")
                return None

            # Update report with only new articles
            report['articles'] = new_articles

            # Try to find today's page
            existing_page = self._find_today_page()
            
            if existing_page:
                # If page exists, add articles to it
                logger.info("Found existing page for today, adding articles...")
                self._add_articles(existing_page['id'], new_articles)
                page_url = existing_page['url']
            else:
                # If no page exists, create new one
                logger.info("Creating new page for today...")
                page = self._create_report_page(report)
                self._add_articles(page['id'], new_articles)
                page_url = page['url']

            # Save newly synced article IDs
            logger.info(f"Saving {len(new_articles)} new article IDs to notion.json")
            self._save_synced_articles([article['id'] for article in new_articles])
            
            logger.info(f"Successfully synced {len(new_articles)} articles to Notion at {page_url}")
            return page_url
                
        except Exception as e:
            logger.error(f"Error syncing report to Notion: {str(e)}")
            raise

    def _find_today_page(self) -> Dict:
        """
        Find today's report page if it exists.
        
        Returns:
            Dict: Page object if found, None otherwise
        """
        today = datetime.now().strftime('%Y-%m-%d')
        try:
            response = self.client.databases.query(
                database_id=self.database_id,
                filter={
                    "and": [
                        {
                            "property": "date",
                            "date": {
                                "equals": today
                            }
                        },
                        {
                            "property": "category",
                            "select": {
                                "equals": "AI Daily Report"
                            }
                        }
                    ]
                }
            )
            
            if response['results']:
                return response['results'][0]
            return None
            
        except Exception as e:
            logger.error(f"Error finding today's page: {str(e)}")
            return None

    @retry(
        stop=stop_after_attempt(3),  # 最多重试3次
        wait=wait_exponential(multiplier=1, min=4, max=10),  # 指数退避重试
        reraise=True
    )
    def _create_report_page(self, report: Dict) -> Dict:
        """Create the main report page in Notion."""
        try:
            # Create the page first
            page = self.client.pages.create(
                parent={'database_id': self.database_id},
                properties=self._get_page_properties(report)
            )
            
            # 添加短暂延迟，避免请求过快
            time.sleep(1)
            
            # Update the page to add cover image
            if report.get('feature_image'):
                self.client.pages.update(
                    page_id=page['id'],
                    cover={
                        "type": "external",
                        "external": {
                            "url": report['feature_image']
                        }
                    }
                )
            
            return page
            
        except Exception as e:
            logger.error(f"Error creating report page: {str(e)}")
            raise
            
    def _get_page_properties(self, report: Dict) -> Dict:
        """Get page properties for Notion page creation."""
        properties = {
            'title': {
                'title': [
                    {
                        'text': {
                            'content': f"{report['title']}"
                        }
                    }
                ]
            },
            'type': {
                'select': {
                    'name': 'Post'
                }
            },
            'status': {
                'select': {
                    'name': 'Published'
                }
            },
            'date': {
                'date': {
                    'start': datetime.strptime(report['date'], '%Y-%m-%d').date().isoformat()
                }
            },
            'category': {
                'select': {
                    'name': report['category']
                }
            },
            'summary': {
                'rich_text': [
                    {'text': {'content': report['summary']}}
                ]
            },
            'slug': {
                'rich_text': [
                    {'text': {'content': report['slug']}}
                ]
            },
            'tags': {
                'multi_select': [
                    {'name': tag} for tag in report.get('tags', ['AI'])
                ]
            }
        }

        # 如果存在slug，添加到properties中
        if report.get('slug'):
            properties['slug'] = {
                'rich_text': [
                    {
                        'text': {
                            'content': f"{report['slug']}"
                        }
                    }
                ]
            }

        return properties

    def _add_articles(self, parent_id: str, markdown_content: str) -> None:
        """Add markdown content as blocks in the Notion page."""
        try:
            # 将字符串内容转换为列表
            if isinstance(markdown_content, str):
                content = markdown_content
            elif isinstance(markdown_content, list):
                content = '\n'.join([article.get('content', '') for article in markdown_content])
            else:
                content = str(markdown_content)

            # 初始化 markdown 解析器
            md = MarkdownIt()
            tokens = md.parse(content)
            blocks = []
            
            i = 0
            while i < len(tokens):
                token = tokens[i]
                
                try:
                    if token.type == 'heading_open':
                        # 处理标题
                        level = int(token.tag[1])  # h1 -> 1, h2 -> 2, etc.
                        content = self._get_token_content(tokens, token)
                        if content:
                            block = {
                                'object': 'block',
                                'type': f'heading_{level}',
                                f'heading_{level}': {
                                    'rich_text': [{
                                        'type': 'text',
                                        'text': {'content': content}
                                    }]
                                }
                            }
                            blocks.append(block)
                    
                    elif token.type == 'paragraph_open':
                        # 处理段落
                        content = self._get_token_content(tokens, token)
                        if content:
                            block = {
                                'object': 'block',
                                'type': 'paragraph',
                                'paragraph': {
                                    'rich_text': self._parse_inline_formats(content)
                                }
                            }
                            blocks.append(block)
                    
                    elif token.type == 'bullet_list_open':
                        # 处理无序列表
                        items = self._get_list_items(tokens, token)
                        for item in items:
                            if item:
                                block = {
                                    'object': 'block',
                                    'type': 'bulleted_list_item',
                                    'bulleted_list_item': {
                                        'rich_text': self._parse_inline_formats(item)
                                    }
                                }
                                blocks.append(block)
                except Exception as e:
                    logger.error(f"Error processing token {token.type}: {str(e)}")
                    continue
                
                i += 1

            # 修改批次处理逻辑
            processed_blocks = []
            for i in range(0, len(blocks), 5):  # 减小批次大小到5
                batch = blocks[i:i + 5]
                if not batch:
                    continue
                    
                retry_count = 0
                max_retries = 3
                
                while retry_count < max_retries:
                    try:
                        # 验证每个块的结构
                        valid_batch = []
                        for block in batch:
                            if not block.get('type'):
                                logger.warning(f"跳过无效块: {block}")
                                continue
                                
                            block_type = block['type']
                            if not block.get(block_type):
                                logger.warning(f"跳过结构不完整的块: {block}")
                                continue
                                
                            valid_batch.append(block)
                        
                        if valid_batch:
                            response = self.client.blocks.children.append(
                                block_id=parent_id,
                                children=valid_batch
                            )
                            processed_blocks.extend(valid_batch)
                            # logger.info(f"成功添加 {len(valid_batch)} 个块")
                            time.sleep(1)  # 添加延迟
                            break  # 成功后跳出重试循环
                            
                    except Exception as e:
                        retry_count += 1
                        error_msg = str(e)
                        logger.error(f"Error adding batch (attempt {retry_count}/{max_retries}): {error_msg}")
                        
                        if retry_count >= max_retries:
                            logger.error(f"Failed to add batch after {max_retries} attempts")
                            logger.error(f"Problematic batch: {json.dumps(batch, ensure_ascii=False)}")
                        else:
                            time.sleep(2 * retry_count)  # 指数退避
            
            # 检查是否所有块都被处理
            if len(processed_blocks) < len(blocks):
                logger.warning(f"只处理了 {len(processed_blocks)}/{len(blocks)} 个块")
                
        except Exception as e:
            logger.error(f"Error in _add_articles: {str(e)}")
            raise

    def _article_exists(self, article_id: str) -> bool:
        """Check if an article already exists in any Notion page by its content ID."""
        try:
            # Query all pages in the database
            response = self.client.databases.query(
                database_id=self.database_id,
                filter={
                    "property": "article_ids",  # This is a multi-select property in Notion
                    "multi_select": {
                        "contains": article_id
                    }
                }
            )
            return len(response['results']) > 0
        except Exception as e:
            logger.error(f"Error checking article existence: {str(e)}")
            return False

    def _cleanup_old_reports(self) -> None:
        """Clean up old reports based on retention policy."""
        try:
            retention_days = self.config.get('retention_days', 30)
            cutoff_date = (
                datetime.now()
                .replace(hour=0, minute=0, second=0, microsecond=0)
                .isoformat()
            )
            
            response = self.client.databases.query(
                database_id=self.database_id,
                filter={
                    'property': 'date',  # Using the date property from your database
                    'date': {
                        'before': cutoff_date
                    }
                }
            )
            
            # Archive old pages
            for page in response['results']:
                self.client.pages.update(
                    page_id=page['id'],
                    archived=True
                )
            
            logger.info(f"Cleaned up {len(response['results'])} old reports")
        except Exception as e:
            logger.error(f"Error cleaning up old reports: {str(e)}")
            raise 

    def _get_token_content(self, tokens: List[Token], current_token: Token) -> str:
        """获取标记之间的文本内容"""
        start_idx = tokens.index(current_token)
        for i in range(start_idx + 1, len(tokens)):
            if tokens[i].type == 'inline':
                return tokens[i].content
        return ""

    def _get_list_items(self, tokens: List[Token], start_token: Token) -> List[str]:
        """获取列表项内容"""
        items = []
        start_idx = tokens.index(start_token)
        current_idx = start_idx + 1
        
        while current_idx < len(tokens):
            token = tokens[current_idx]
            if token.type in ['bullet_list_close', 'ordered_list_close']:
                break
            if token.type == 'inline':
                items.append(token.content)
            current_idx += 1
        
        return items

    def _parse_inline_formats(self, text: str) -> List[Dict]:
        """解析内联格式（加粗、斜体、链接等）"""
        rich_text = []
        current_text = ""
        i = 0
        
        while i < len(text):
            # 处理下载链接
            if text.startswith("下载地址：", i):
                if current_text:
                    rich_text.append({
                        'type': 'text',
                        'text': {'content': current_text}
                    })
                    current_text = ""
                
                # 添加"下载地址："文本（红色加粗）
                rich_text.append({
                    'type': 'text',
                    'text': {'content': "下载地址："},
                    'annotations': {
                        'color': 'red',
                        'bold': True
                    }
                })
                i += len("下载地址：")
                continue
            if text.startswith("网站地址：", i):
                if current_text:
                    rich_text.append({
                        'type': 'text',
                        'text': {'content': current_text}
                    })
                    current_text = ""
                
                # 添加"下载地址："文本（红色加粗）
                rich_text.append({
                    'type': 'text',
                    'text': {'content': "网站地址："},
                    'annotations': {
                        'color': 'red',
                        'bold': True
                    }
                })
                i += len("网站地址：")
                continue
            
            # 处理链接格式 [text](url)
            if text[i] == '[':
                link_end = text.find(']', i)
                url_start = text.find('(', link_end)
                url_end = text.find(')', url_start) if url_start != -1 else -1
                
                if link_end != -1 and url_start != -1 and url_end != -1:
                    # 添加之前的普通文本
                    if current_text:
                        rich_text.append({
                            'type': 'text',
                            'text': {'content': current_text}
                        })
                        current_text = ""
                    
                    # 添加链接
                    link_text = text[i+1:link_end]
                    url = text[url_start+1:url_end]
                    
                    # 如果链接文本是"夸克网盘"，添加红色加粗样式
                    annotations = {}
                    if link_text == "夸克网盘":
                        annotations = {
                            'color': 'red',
                            'bold': True
                        }
                    
                    rich_text.append({
                        'type': 'text',
                        'text': {
                            'content': link_text,
                            'link': {'url': url}
                        },
                        'annotations': annotations
                    })
                    i = url_end + 1
                    continue
            
            current_text += text[i]
            i += 1
        
        if current_text:
            rich_text.append({
                'type': 'text',
                'text': {'content': current_text}
            })
        
        return rich_text 