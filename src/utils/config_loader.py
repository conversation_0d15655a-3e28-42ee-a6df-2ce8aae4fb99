import yaml
import logging
import os
from pathlib import Path

def load_config():
    """加载配置文件"""
    try:
        # 获取项目根目录
        root_dir = str(Path(__file__).parent.parent.parent)
        
        config_path = os.path.join(root_dir, 'config', 'config.yaml')
        with open(config_path, 'r', encoding='utf-8') as f:
            config_str = f.read()
            
        # 替换${ROOT_DIR}变量
        config_str = config_str.replace("${ROOT_DIR}", root_dir)
        
        # 加载YAML
        config = yaml.safe_load(config_str)
        
        return config
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        return None 