import praw
import os
from dotenv import load_dotenv
import datetime
import requests
from bs4 import BeautifulSoup
load_dotenv()

# 导入DAO类
from dao.subreddit_dao import SubredditDao
from dao.post_dao import PostDao
from dao.comment_dao import CommentDao
from utils.logger import get_logger

logger = get_logger("spider_reddit")

REDDIT_CLIENT_ID = os.environ.get("REDDIT_CLIENT_ID")
REDDIT_CLIENT_SECRET = os.environ.get("REDDIT_CLIENT_SECRET")


def connect_to_reddit():
    """连接到Reddit API"""
    reddit = praw.Reddit(
        client_id=REDDIT_CLIENT_ID,
        client_secret=REDDIT_CLIENT_SECRET,
        user_agent='u/Taiwanese'
    )
    return reddit

def get_subreddit_rank(subreddit_name):
    """
    获取子版块的排名信息
    
    参数:
    - subreddit_name: 子版块名称
    
    返回:
    - 排名数字，如果获取失败则返回None
    """
    try:
        # 方法1: 使用subredditstats.com网站
        try:
            url = f"https://subredditstats.com/r/{subreddit_name.lower()}"
            response = requests.get(url, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                # 查找包含排名信息的元素
                rank_element = soup.select_one('.subreddit-rank')
                
                if rank_element:
                    # 提取排名数字
                    rank_text = rank_element.get_text().strip()
                    # 格式通常为 "#1234 Ranked Subreddit"
                    if '#' in rank_text:
                        rank = int(rank_text.split('#')[1].split(' ')[0])
                        logger.info(f"[方法1] 获取到子版块 {subreddit_name} 排名: {rank}")
                        return rank
        except Exception as e:
            logger.warning(f"[方法1] 获取子版块排名时出错: {str(e)}")
        
        # 方法2: 使用frontpagemetrics.com网站
        try:
            url = f"https://frontpagemetrics.com/r/{subreddit_name.lower()}"
            response = requests.get(url, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                # 查找包含排名信息的元素
                meta_elements = soup.select('.stat-box')
                
                for element in meta_elements:
                    title_element = element.select_one('.stat-title')
                    if title_element and 'rank' in title_element.get_text().lower():
                        value_element = element.select_one('.stat-value')
                        if value_element:
                            try:
                                # 提取排名数字
                                rank_text = value_element.get_text().strip()
                                rank = int(rank_text.replace(',', '').replace('.', ''))
                                logger.info(f"[方法2] 获取到子版块 {subreddit_name} 排名: {rank}")
                                return rank
                            except ValueError:
                                pass
        except Exception as e:
            logger.warning(f"[方法2] 获取子版块排名时出错: {str(e)}")
        
        # 方法3: 使用PRAW API获取订阅人数，作为排名的近似指标
        try:
            reddit = connect_to_reddit()
            subreddit = reddit.subreddit(subreddit_name)
            subscribers = subreddit.subscribers
            
            # 我们不能直接获取排名，但可以用订阅人数作为排名的近似估计
            # 这里我们只是记录订阅人数，实际排名需要通过比较不同子版块的订阅人数来确定
            logger.info(f"[方法3] 子版块 {subreddit_name} 订阅人数: {subscribers}")
            
            # 根据订阅人数粗略估计排名
            # 这只是一个非常粗略的近似，实际排名可能差异很大
            estimated_rank = None
            if subscribers >= 20000000:  # 2千万以上
                estimated_rank = 50
            elif subscribers >= 10000000:  # 1千万以上
                estimated_rank = 100
            elif subscribers >= 5000000:  # 5百万以上
                estimated_rank = 200
            elif subscribers >= 1000000:  # 1百万以上
                estimated_rank = 500
            elif subscribers >= 500000:  # 50万以上
                estimated_rank = 1000
            elif subscribers >= 100000:  # 10万以上
                estimated_rank = 2000
            elif subscribers >= 50000:  # 5万以上
                estimated_rank = 5000
            elif subscribers >= 10000:  # 1万以上
                estimated_rank = 10000
            elif subscribers >= 1000:  # 1千以上
                estimated_rank = 20000
            else:
                estimated_rank = 50000
                
            if estimated_rank:
                logger.info(f"[方法3] 根据订阅人数估计子版块 {subreddit_name} 排名约为: {estimated_rank}")
                return estimated_rank
        except Exception as e:
            logger.warning(f"[方法3] 获取子版块订阅人数时出错: {str(e)}")
            
        # 如果所有方法都失败了，返回None
        logger.warning(f"无法获取子版块 {subreddit_name} 的排名")
        return None
    except Exception as e:
        logger.error(f"获取子版块排名流程出错: {str(e)}")
        return None

def get_top_posts(subreddit_name):
    """获取子版块的热门帖子"""
    reddit = connect_to_reddit()
    subreddit = reddit.subreddit(subreddit_name)
    for submission in subreddit.top(limit=5):
        print(submission.title)

def get_new_posts(subreddit_name, limit=10):
    """获取子版块的最新帖子"""
    reddit = connect_to_reddit()
    subreddit = reddit.subreddit(subreddit_name)
    posts = []
    
    for submission in subreddit.new(limit=limit):
        posts.append({
            "id": submission.id,
            "title": submission.title,
            "score": submission.score,
            "url": submission.url,
            "created_utc": submission.created_utc,
            "num_comments": submission.num_comments,
            "selftext": submission.selftext,
            "author": str(submission.author)
        })
    
    return posts

def get_hot_posts(subreddit_name, limit=10):
    """获取热门帖子"""
    reddit = connect_to_reddit()
    subreddit = reddit.subreddit(subreddit_name)
    posts = []
    
    for submission in subreddit.hot(limit=limit):
        posts.append({"title": submission.title, "score": submission.score})
    
    return posts

def get_rising_posts(subreddit_name, limit=10):
    """获取上升中的帖子"""
    reddit = connect_to_reddit()
    subreddit = reddit.subreddit(subreddit_name)
    posts = []
    
    for submission in subreddit.rising(limit=limit):
        posts.append({"title": submission.title, "score": submission.score})
    
    return posts

def get_controversial_posts(subreddit_name, limit=10, time_filter="day"):
    """获取有争议的帖子"""
    reddit = connect_to_reddit()
    subreddit = reddit.subreddit(subreddit_name)
    posts = []
    
    # time_filter可选: hour, day, week, month, year, all
    for submission in subreddit.controversial(limit=limit, time_filter=time_filter):
        posts.append({"title": submission.title, "score": submission.score})
    
    return posts

def get_post_comments(post_id, limit=10):
    """获取指定帖子的前N条评论"""
    reddit = connect_to_reddit()
    submission = reddit.submission(id=post_id)
    
    # 确保加载所有评论
    submission.comments.replace_more(limit=0)
    
    comments = []
    # 获取前limit条评论
    for i, comment in enumerate(submission.comments):
        if i >= limit:
            break
            
        comments.append({
            "id": comment.id,
            "author": str(comment.author),
            "body": comment.body,
            "score": comment.score,
            "created_utc": comment.created_utc,
            "is_submitter": comment.is_submitter  # 是否是原帖作者
        })
    
    return comments

def get_subreddit_posts_with_comments(subreddit_name, posts_limit=5, comments_limit=10):
    """获取子版块的帖子及其评论"""
    reddit = connect_to_reddit()
    subreddit = reddit.subreddit(subreddit_name)
    results = []
    
    for submission in subreddit.hot(limit=posts_limit):
        # 获取帖子基本信息
        post_data = {
            "id": submission.id,
            "title": submission.title,
            "score": submission.score,
            "url": submission.url,
            "author": str(submission.author),
            "num_comments": submission.num_comments,
            "comments": []
        }
        
        # 获取评论
        submission.comments.replace_more(limit=0)  # 展开评论
        for i, comment in enumerate(submission.comments):
            if i >= comments_limit:
                break
                
            post_data["comments"].append({
                "id": comment.id,
                "author": str(comment.author),
                "body": comment.body,
                "score": comment.score
            })
        
        results.append(post_data)
    
    return results

def format_and_print_reddit_data(subreddit_name, posts_limit=5, comments_limit=10):
    """优化格式化打印Reddit数据"""
    reddit = connect_to_reddit()
    subreddit = reddit.subreddit(subreddit_name)
    
    print("\n" + "="*80)
    print(f"子版块: r/{subreddit_name}")
    print(f"订阅人数: {subreddit.subscribers}")
    print(f"创建时间: {datetime.datetime.fromtimestamp(subreddit.created_utc)}")
    print(f"描述: {subreddit.public_description[:100]}..." if len(subreddit.public_description) > 100 
          else f"描述: {subreddit.public_description}")
    print("="*80 + "\n")
    
    # 获取热门帖子
    for i, submission in enumerate(subreddit.hot(limit=posts_limit), 1):
        # 获取帖子详细信息
        post_data = {
            "id": submission.id,
            "title": submission.title,
            "score": submission.score,
            "upvote_ratio": submission.upvote_ratio,
            "url": submission.url,
            "author": str(submission.author),
            "created_utc": datetime.datetime.fromtimestamp(submission.created_utc),
            "num_comments": submission.num_comments,
            "is_original_content": submission.is_original_content,
            "is_self": submission.is_self,
            "selftext": submission.selftext
        }
        
        # 打印帖子信息
        print(f"帖子 {i}/{posts_limit} " + "-"*60)
        print(f"ID: {post_data['id']}")
        print(f"标题: {post_data['title']}")
        print(f"作者: {post_data['author']}")
        print(f"发布时间: {post_data['created_utc']}")
        print(f"评分: {post_data['score']} (赞成率: {int(post_data['upvote_ratio']*100)}%)")
        print(f"评论数: {post_data['num_comments']}")
        
        # 如果是文本帖子，显示内容
        if post_data['is_self'] and post_data['selftext']:
            print("\n帖子内容:")
            content = post_data['selftext']
            # 如果内容太长，截断显示
            if len(content) > 300:
                print(f"{content[:300]}...\n[内容过长，已截断]")
            else:
                print(content)
        else:
            print(f"\n链接: {post_data['url']}")
        
        # 获取并打印评论
        submission.comments.replace_more(limit=0)  # 展开评论
        
        if submission.comments:
            print("\n评论:")
            for j, comment in enumerate(submission.comments[:comments_limit], 1):
                print(f"  评论 {j}/{min(comments_limit, len(submission.comments))}")
                print(f"  作者: {comment.author}")
                print(f"  时间: {datetime.datetime.fromtimestamp(comment.created_utc)}")
                print(f"  评分: {comment.score}")
                print(f"  内容: {comment.body[:200]}..." if len(comment.body) > 200 
                      else f"  内容: {comment.body}")
                
                # 如果不是最后一条评论，添加分隔线
                if j < min(comments_limit, len(submission.comments)):
                    print("  " + "-"*30)
        else:
            print("\n暂无评论")
            
        # 添加帖子间的分隔线
        print("\n" + "="*80 + "\n")

def get_posts_by_ranking(subreddit_name, ranking="hot", limit=10, time_filter="all"):
    """
    根据不同排序方式获取帖子数据
    
    参数:
    - subreddit_name: 子版块名称
    - ranking: 排序方式，可选 'hot', 'new', 'top', 'rising', 'controversial'
    - limit: 获取帖子数量
    - time_filter: 时间过滤器，适用于'top'和'controversial'，可选 'hour', 'day', 'week', 'month', 'year', 'all'
    
    返回:
    - 帖子列表
    """
    reddit = connect_to_reddit()
    subreddit = reddit.subreddit(subreddit_name)
    posts = []
    
    # 根据ranking参数选择对应的排序方法
    if ranking == "hot":
        submissions = subreddit.hot(limit=limit)
    elif ranking == "new":
        submissions = subreddit.new(limit=limit)
    elif ranking == "top":
        submissions = subreddit.top(limit=limit, time_filter=time_filter)
    elif ranking == "rising":
        submissions = subreddit.rising(limit=limit)
    elif ranking == "controversial":
        submissions = subreddit.controversial(limit=limit, time_filter=time_filter)
    else:
        raise ValueError("无效的排序方式，请选择'hot', 'new', 'top', 'rising'或'controversial'")
    
    # 处理获取到的帖子
    for submission in submissions:
        posts.append({
            "id": submission.id,
            "title": submission.title,
            "score": submission.score,
            "upvote_ratio": submission.upvote_ratio,
            "url": submission.url,
            "author": str(submission.author),
            "created_utc": datetime.datetime.fromtimestamp(submission.created_utc),
            "num_comments": submission.num_comments,
            "is_original_content": submission.is_original_content,
            "is_self": submission.is_self,
            "selftext": submission.selftext if hasattr(submission, 'selftext') else "",
            "permalink": submission.permalink,
            "subreddit": submission.subreddit.display_name
        })
    
    return posts

def get_post_by_id(post_id, comments_limit=10):
    """
    根据帖子ID获取详细信息和评论
    
    参数:
    - post_id: 帖子ID
    - comments_limit: 获取评论数量
    
    返回:
    - 包含帖子详情和评论的字典
    """
    reddit = connect_to_reddit()
    submission = reddit.submission(id=post_id)
    
    # 获取帖子详细信息
    post_data = {
        "id": submission.id,
        "title": submission.title,
        "score": submission.score,
        "upvote_ratio": submission.upvote_ratio,
        "url": submission.url,
        "author": str(submission.author),
        "created_utc": datetime.datetime.fromtimestamp(submission.created_utc),
        "num_comments": submission.num_comments,
        "is_original_content": submission.is_original_content,
        "is_self": submission.is_self,
        "selftext": submission.selftext if hasattr(submission, 'selftext') else "",
        "subreddit": submission.subreddit.display_name,
        "permalink": submission.permalink,
        "comments": []
    }
    
    # 获取评论
    submission.comments.replace_more(limit=0)  # 展开评论
    
    for i, comment in enumerate(submission.comments):
        if i >= comments_limit:
            break
            
        post_data["comments"].append({
            "id": comment.id,
            "author": str(comment.author),
            "body": comment.body,
            "score": comment.score,
            "created_utc": datetime.datetime.fromtimestamp(comment.created_utc),
            "is_submitter": comment.is_submitter
        })
    
    return post_data

def save_subreddit_info(subreddit_name):
    """
    获取并保存子版块信息
    
    参数:
    - subreddit_name: 子版块名称
    
    返回:
    - 子版块ID或None
    """
    try:
        reddit = connect_to_reddit()
        subreddit = reddit.subreddit(subreddit_name)
        
        # 获取子版块排名
        rank = get_subreddit_rank(subreddit_name)
        
        # 构建子版块URL
        url = f"https://www.reddit.com/r/{subreddit_name}"
        
        # 准备子版块数据
        subreddit_data = {
            "name": subreddit.display_name.lower(),  #, 使用小写作为唯一标识
            "display_name": subreddit.display_name,
            "subscribers": subreddit.subscribers,
            "public_description": subreddit.public_description,
            "created_utc": datetime.datetime.fromtimestamp(subreddit.created_utc),
            "rank": rank,
            "url": url
        }
        
        # 保存到数据库
        subreddit_id = SubredditDao.insert_or_update_subreddit(subreddit_data)
        
        if subreddit_id:
            logger.info(f"子版块信息保存成功: {subreddit_name}")
        else:
            logger.warning(f"子版块信息保存失败: {subreddit_name}")
            
        return subreddit_id
    except Exception as e:
        logger.error(f"保存子版块信息出错: {str(e)}")
        return None

def save_posts_and_comments(posts, with_comments=True, comments_limit=10):
    """
    保存帖子和评论数据
    
    参数:
    - posts: 帖子数据列表
    - with_comments: 是否同时保存评论
    - comments_limit: 每个帖子保存的评论数量限制
    
    返回:
    - 成功保存的帖子数量
    """
    success_count = 0
    
    for post in posts:
        try:
            # 保存帖子数据
            result = PostDao.insert_or_update_post(post)
            
            if result:
                success_count += 1
                logger.debug(f"帖子保存成功: {post['id']}")
                
                # 如果需要保存评论
                if with_comments and post.get('comments'):
                    comments = post['comments']
                    for comment in comments[:comments_limit]:
                        CommentDao.insert_or_update_comment(comment, post['id'])
                # 如果需要获取并保存评论，但帖子数据中没有评论信息
                elif with_comments and post['num_comments'] > 0:
                    full_post = get_post_by_id(post['id'], comments_limit)
                    if full_post and 'comments' in full_post:
                        for comment in full_post['comments']:
                            CommentDao.insert_or_update_comment(comment, post['id'])
            else:
                logger.warning(f"帖子保存失败: {post['id']}")
        except Exception as e:
            logger.error(f"保存帖子出错: {post['id']} - {str(e)}")
    
    return success_count

def crawl_and_save_by_ranking(subreddit_name, ranking="hot", posts_limit=10, comments_limit=10, time_filter="all"):
    """
    根据排序方式抓取并保存Reddit数据
    
    参数:
    - subreddit_name: 子版块名称
    - ranking: 排序方式
    - posts_limit: 帖子数量限制
    - comments_limit: 评论数量限制
    - time_filter: 时间过滤器
    
    返回:
    - 包含抓取统计信息的字典
    """
    results = {
        "subreddit": subreddit_name,
        "ranking": ranking,
        "time_filter": time_filter if ranking in ['top', 'controversial'] else None,
        "posts_saved": 0,
        "posts_failed": 0,
        "start_time": datetime.datetime.now()
    }
    
    try:
        # 保存子版块信息
        save_subreddit_info(subreddit_name)
        
        # 获取帖子列表
        posts = get_posts_by_ranking(subreddit_name, ranking, posts_limit, time_filter)
        
        if posts:
            # 保存帖子和评论
            success_count = save_posts_and_comments(posts, True, comments_limit)
            
            results["posts_saved"] = success_count
            results["posts_failed"] = len(posts) - success_count
        else:
            logger.warning(f"未找到帖子: {subreddit_name} - {ranking}")
            
    except Exception as e:
        logger.error(f"抓取保存出错: {subreddit_name} - {ranking} - {str(e)}")
        results["error"] = str(e)
    
    results["end_time"] = datetime.datetime.now()
    results["duration"] = (results["end_time"] - results["start_time"]).total_seconds()
    
    return results

def format_and_print_by_ranking(subreddit_name, ranking="hot", posts_limit=3, comments_limit=10, time_filter="all", save_data=True):
    """根据不同排序方式打印Reddit数据并可选择保存到数据库"""
    reddit = connect_to_reddit()
    subreddit = reddit.subreddit(subreddit_name)
    
    # 获取子版块排名
    rank = get_subreddit_rank(subreddit_name)
    
    print("\n" + "="*80)
    print(f"子版块: r/{subreddit_name}")
    if rank:
        print(f"子版块排名: #{rank}")
    print(f"排序方式: {ranking.upper()}" + (f" - {time_filter}" if ranking in ['top', 'controversial'] else ""))
    print(f"订阅人数: {subreddit.subscribers}")
    print(f"创建时间: {datetime.datetime.fromtimestamp(subreddit.created_utc)}")
    print(f"URL: https://www.reddit.com/r/{subreddit_name}")
    print(f"描述: {subreddit.public_description[:100]}..." if len(subreddit.public_description) > 100 
          else f"描述: {subreddit.public_description}")
    print("="*80 + "\n")
    
    # 如果需要保存数据，先保存子版块信息
    if save_data:
        save_subreddit_info(subreddit_name)
    
    # 获取指定排序方式的帖子
    posts = get_posts_by_ranking(subreddit_name, ranking, posts_limit, time_filter)
    
    # 如果需要保存数据，保存帖子信息
    if save_data and posts:
        save_posts_and_comments(posts, False)  # 先只保存帖子
        
    for i, post in enumerate(posts, 1):
        # 打印帖子信息
        print(f"帖子 {i}/{len(posts)} " + "-"*60)
        print(f"ID: {post['id']}")
        print(f"标题: {post['title']}")
        print(f"作者: {post['author']}")
        print(f"发布时间: {post['created_utc']}")
        print(f"评分: {post['score']} (赞成率: {int(post['upvote_ratio']*100)}%)")
        print(f"评论数: {post['num_comments']}")
        print(f"链接: https://www.reddit.com{post['permalink']}")
        
        # 如果是文本帖子，显示内容
        if post['is_self'] and post['selftext']:
            print("\n帖子内容:")
            content = post['selftext']
            # 如果内容太长，截断显示
            if len(content) > 300:
                print(f"{content[:300]}...\n[内容过长，已截断]")
            else:
                print(content)
        else:
            print(f"\n外部链接: {post['url']}")
        
        # 获取评论
        post_with_comments = get_post_by_id(post['id'], comments_limit)
        
        # 如果需要保存数据，保存评论信息
        if save_data and post_with_comments['comments']:
            for comment in post_with_comments['comments']:
                CommentDao.insert_or_update_comment(comment, post['id'])
        
        if post_with_comments['comments']:
            print("\n评论:")
            for j, comment in enumerate(post_with_comments['comments'], 1):
                print(f"  评论 {j}/{len(post_with_comments['comments'])}")
                print(f"  ID: {comment['id']}")
                print(f"  作者: {comment['author']}" + (" (原帖作者)" if comment['is_submitter'] else ""))
                print(f"  时间: {comment['created_utc']}")
                print(f"  评分: {comment['score']}")
                print(f"  内容: {comment['body'][:200]}..." if len(comment['body']) > 200 
                      else f"  内容: {comment['body']}")
                
                if j < len(post_with_comments['comments']):
                    print("  " + "-"*30)
        else:
            print("\n暂无评论")
            
        print("\n" + "="*80 + "\n")

def format_and_print_post_by_id(post_id, comments_limit=10, save_data=True):
    """格式化打印指定ID的帖子，并可选择保存到数据库"""
    post_data = get_post_by_id(post_id, comments_limit)
    
    # 如果需要保存数据
    if save_data:
        # 先保存子版块信息
        save_subreddit_info(post_data['subreddit'])
        
        # 保存帖子数据
        PostDao.insert_or_update_post(post_data)
        
        # 保存评论数据
        for comment in post_data['comments']:
            CommentDao.insert_or_update_comment(comment, post_id)
    
    print("\n" + "="*80)
    print(f"帖子ID: {post_data['id']}")
    print(f"子版块: r/{post_data['subreddit']}")
    print("="*80 + "\n")
    
    
    print(f"标题: {post_data['title']}")
    print(f"作者: {post_data['author']}")
    print(f"发布时间: {post_data['created_utc']}")
    print(f"评分: {post_data['score']} (赞成率: {int(post_data['upvote_ratio']*100)}%)")
    print(f"评论数: {post_data['num_comments']}")
    print(f"链接: https://www.reddit.com{post_data['permalink']}")
    
    # 如果是文本帖子，显示内容
    if post_data['is_self'] and post_data['selftext']:
        print("\n帖子内容:")
        content = post_data['selftext']
        # 如果内容太长，截断显示
        if len(content) > 500:
            print(f"{content[:500]}...\n[内容过长，已截断]")
        else:
            print(content)
    else:
        print(f"\n外部链接: {post_data['url']}")
    
    # 打印评论
    if post_data['comments']:
        print("\n评论:")
        for i, comment in enumerate(post_data['comments'], 1):
            print(f"  评论 {i}/{len(post_data['comments'])}")
            print(f"  ID: {comment['id']}")
            print(f"  作者: {comment['author']}" + (" (原帖作者)" if comment['is_submitter'] else ""))
            print(f"  时间: {comment['created_utc']}")
            print(f"  评分: {comment['score']}")
            print(f"  内容: {comment['body'][:300]}..." if len(comment['body']) > 300 
                  else f"  内容: {comment['body']}")
            
            if i < len(post_data['comments']):
                print("  " + "-"*30)
    else:
        print("\n暂无评论")
        
    print("\n" + "="*80)

def reddit_main(subreddit_name="Taiwanese", save_data=True):
    """
    Reddit爬虫主函数
    
    参数:
    - subreddit_name: 子版块名称
    - save_data: 是否保存数据到数据库
    """
    try:
        # 确保数据表已创建
        if save_data:
            SubredditDao.create_table_if_not_exists()
            PostDao.create_table_if_not_exists()
            CommentDao.create_table_if_not_exists()
            
        # 抓取并保存不同排序方式的数据
        hot_result = crawl_and_save_by_ranking(subreddit_name, "hot", 5, 10)
        new_result = crawl_and_save_by_ranking(subreddit_name, "new", 5, 10)
        top_result = crawl_and_save_by_ranking(subreddit_name, "top", 5, 10, "week")
        
        # 打印抓取统计信息
        logger.info(f"抓取完成 - {subreddit_name}")
        logger.info(f"热门帖子: 保存 {hot_result['posts_saved']} 条, 失败 {hot_result['posts_failed']} 条")
        logger.info(f"最新帖子: 保存 {new_result['posts_saved']} 条, 失败 {new_result['posts_failed']} 条")
        logger.info(f"本周最佳: 保存 {top_result['posts_saved']} 条, 失败 {top_result['posts_failed']} 条")
        
        return True
    except Exception as e:
        logger.error(f"Reddit爬虫执行出错: {str(e)}")
        return False

if __name__ == "__main__":
    # 示例1: 按排序方式获取数据
    subreddit_name = 'Taiwanese'
    
    # 获取热门帖子数据并保存
    format_and_print_by_ranking(subreddit_name, ranking="hot", posts_limit=2, comments_limit=10, save_data=True)
    
    # 获取最新帖子数据并保存
    # format_and_print_by_ranking(subreddit_name, ranking="new", posts_limit=2, comments_limit=10, save_data=True)
    
    # 获取本周最佳帖子并保存
    # format_and_print_by_ranking(subreddit_name, ranking="top", posts_limit=2, comments_limit=10, time_filter="week", save_data=True)
    
    # 示例2: 获取特定ID的帖子并保存
    # 首先获取一个帖子ID
    # posts = get_posts_by_ranking(subreddit_name, "hot", 1)
    # if posts:
    #     post_id = posts[0]['id']
    #     # 获取并打印特定ID的帖子，同时保存到数据库
    #     format_and_print_post_by_id(post_id, comments_limit=10, save_data=True) 