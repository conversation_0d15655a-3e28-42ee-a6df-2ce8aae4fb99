import os
import psycopg2
import datetime
from database.db_pool import DatabasePool
from utils.logger import get_logger

logger = get_logger('post_dao')

class PostDao:
    """Reddit帖子数据访问对象"""
    
    @staticmethod
    def create_table_if_not_exists():
        """创建帖子表（如果不存在）"""
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            # 创建帖子表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS reddit_posts (
                id SERIAL PRIMARY KEY, -- 自增主键
                post_id VARCHAR(20) NOT NULL UNIQUE, -- Reddit帖子ID，唯一
                subreddit VARCHAR(100) NOT NULL, -- 所属子版块名称
                title VARCHAR(300) NOT NULL, -- 帖子标题
                author VARCHAR(100), -- 作者用户名
                created_utc TIMESTAMP, -- 发布时间
                score INTEGER DEFAULT 0, -- 评分/得分
                upvote_ratio FLOAT, -- 赞成率
                url TEXT, -- 原始URL
                permalink TEXT, -- Reddit永久链接
                num_comments INTEGER DEFAULT 0, -- 评论数量
                is_self BOOLEAN DEFAULT FALSE, -- 是否为自发帖子
                selftext TEXT, -- 帖子文本内容
                is_original_content BOOLEAN DEFAULT FALSE, -- 是否为原创内容
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 记录更新时间
            );
            
            -- 创建索引
            CREATE INDEX IF NOT EXISTS idx_post_subreddit ON reddit_posts(subreddit);
            CREATE INDEX IF NOT EXISTS idx_post_author ON reddit_posts(author);
            CREATE INDEX IF NOT EXISTS idx_post_created ON reddit_posts(created_utc);
            CREATE INDEX IF NOT EXISTS idx_post_score ON reddit_posts(score);
            
            -- 添加表注释
            COMMENT ON TABLE reddit_posts IS 'Reddit帖子信息表';
            COMMENT ON COLUMN reddit_posts.id IS '主键ID';
            COMMENT ON COLUMN reddit_posts.post_id IS 'Reddit帖子ID';
            COMMENT ON COLUMN reddit_posts.subreddit IS '所属子版块名称';
            COMMENT ON COLUMN reddit_posts.title IS '帖子标题';
            COMMENT ON COLUMN reddit_posts.author IS '作者用户名';
            COMMENT ON COLUMN reddit_posts.created_utc IS '发布时间';
            COMMENT ON COLUMN reddit_posts.score IS '评分/得分';
            COMMENT ON COLUMN reddit_posts.upvote_ratio IS '赞成率';
            COMMENT ON COLUMN reddit_posts.url IS '原始URL';
            COMMENT ON COLUMN reddit_posts.permalink IS 'Reddit永久链接';
            COMMENT ON COLUMN reddit_posts.num_comments IS '评论数量';
            COMMENT ON COLUMN reddit_posts.is_self IS '是否为自发帖子';
            COMMENT ON COLUMN reddit_posts.selftext IS '帖子文本内容';
            COMMENT ON COLUMN reddit_posts.is_original_content IS '是否为原创内容';
            COMMENT ON COLUMN reddit_posts.created_at IS '记录创建时间';
            COMMENT ON COLUMN reddit_posts.updated_at IS '记录更新时间';
            
            -- 更新时间触发器
            CREATE OR REPLACE FUNCTION update_post_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ LANGUAGE 'plpgsql';
            
            -- 创建触发器
            DROP TRIGGER IF EXISTS update_post_modtime ON reddit_posts;
            CREATE TRIGGER update_post_modtime
                BEFORE UPDATE ON reddit_posts
                FOR EACH ROW
                EXECUTE FUNCTION update_post_updated_at_column();
            """)
            
            conn.commit()
            logger.info("帖子表创建/验证成功")
            return True
        except Exception as e:
            logger.error(f"创建帖子表失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def insert_or_update_post(post_data):
        """
        插入或更新帖子信息
        
        参数:
            post_data: 帖子数据字典
        
        返回:
            插入/更新成功返回True，失败返回False
        """
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            # 检查帖子是否已存在
            cursor.execute("""
            SELECT id FROM reddit_posts WHERE post_id = %s
            """, (post_data['id'],))
            
            result = cursor.fetchone()
            
            if result:
                # 更新现有帖子
                cursor.execute("""
                UPDATE reddit_posts SET
                    title = %s,
                    author = %s,
                    score = %s,
                    upvote_ratio = %s,
                    num_comments = %s,
                    is_self = %s,
                    selftext = %s,
                    is_original_content = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE post_id = %s
                """, (
                    post_data['title'],
                    post_data['author'],
                    post_data['score'],
                    post_data.get('upvote_ratio', 0),
                    post_data['num_comments'],
                    post_data.get('is_self', False),
                    post_data.get('selftext', ''),
                    post_data.get('is_original_content', False),
                    post_data['id']
                ))
                
                logger.debug(f"更新帖子: {post_data['id']} - {post_data['title'][:30]}")
            else:
                # 插入新帖子
                cursor.execute("""
                INSERT INTO reddit_posts (
                    post_id, subreddit, title, author, created_utc, 
                    score, upvote_ratio, url, permalink, num_comments, 
                    is_self, selftext, is_original_content
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    post_data['id'],
                    post_data.get('subreddit', ''),
                    post_data['title'],
                    post_data['author'],
                    post_data.get('created_utc'),
                    post_data['score'],
                    post_data.get('upvote_ratio', 0),
                    post_data.get('url', ''),
                    post_data.get('permalink', ''),
                    post_data['num_comments'],
                    post_data.get('is_self', False),
                    post_data.get('selftext', ''),
                    post_data.get('is_original_content', False)
                ))
                
                logger.info(f"插入新帖子: {post_data['id']} - {post_data['title'][:30]}")
            
            conn.commit()
            return True
        except Exception as e:
            logger.error(f"插入/更新帖子失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_post_by_id(post_id):
        """
        通过ID获取帖子信息
        
        参数:
            post_id: Reddit帖子ID
            
        返回:
            帖子数据字典，未找到则返回None
        """
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
            SELECT 
                id, post_id, subreddit, title, author, created_utc,
                score, upvote_ratio, url, permalink, num_comments,
                is_self, selftext, is_original_content,
                created_at, updated_at
            FROM reddit_posts 
            WHERE post_id = %s
            """, (post_id,))
            
            result = cursor.fetchone()
            
            if result:
                return {
                    'id': result[0],
                    'post_id': result[1],
                    'subreddit': result[2],
                    'title': result[3],
                    'author': result[4],
                    'created_utc': result[5],
                    'score': result[6],
                    'upvote_ratio': result[7],
                    'url': result[8],
                    'permalink': result[9],
                    'num_comments': result[10],
                    'is_self': result[11],
                    'selftext': result[12],
                    'is_original_content': result[13],
                    'created_at': result[14],
                    'updated_at': result[15]
                }
            else:
                return None
        except Exception as e:
            logger.error(f"获取帖子信息失败: {e}")
            return None
        finally:
            DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_posts_by_subreddit(subreddit, limit=50, offset=0, sort_by='created_utc', sort_order='DESC'):
        """
        获取指定子版块的帖子列表
        
        参数:
            subreddit: 子版块名称
            limit: 返回结果数量限制
            offset: 分页偏移量
            sort_by: 排序字段
            sort_order: 排序方向 ('ASC'或'DESC')
            
        返回:
            帖子数据字典列表
        """
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            # 构建查询SQL
            query = f"""
            SELECT 
                id, post_id, subreddit, title, author, created_utc,
                score, upvote_ratio, url, permalink, num_comments,
                is_self, selftext, is_original_content,
                created_at, updated_at
            FROM reddit_posts 
            WHERE subreddit = %s
            ORDER BY {sort_by} {sort_order}
            LIMIT %s OFFSET %s
            """
            
            cursor.execute(query, (subreddit, limit, offset))
            
            results = cursor.fetchall()
            
            posts = []
            for row in results:
                posts.append({
                    'id': row[0],
                    'post_id': row[1],
                    'subreddit': row[2],
                    'title': row[3],
                    'author': row[4],
                    'created_utc': row[5],
                    'score': row[6],
                    'upvote_ratio': row[7],
                    'url': row[8],
                    'permalink': row[9],
                    'num_comments': row[10],
                    'is_self': row[11],
                    'selftext': row[12],
                    'is_original_content': row[13],
                    'created_at': row[14],
                    'updated_at': row[15]
                })
            
            return posts
        except Exception as e:
            logger.error(f"获取子版块帖子列表失败: {e}")
            return []
        finally:
            DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_top_posts(limit=10, days=7):
        """
        获取一段时间内的热门帖子
        
        参数:
            limit: 返回结果数量限制
            days: 时间范围（天）
            
        返回:
            热门帖子列表
        """
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            # 计算日期范围
            date_threshold = datetime.datetime.now() - datetime.timedelta(days=days)
            
            cursor.execute("""
            SELECT 
                id, post_id, subreddit, title, author, created_utc,
                score, upvote_ratio, url, permalink, num_comments,
                is_self, selftext, is_original_content,
                created_at, updated_at
            FROM reddit_posts 
            WHERE created_utc > %s
            ORDER BY score DESC
            LIMIT %s
            """, (date_threshold, limit))
            
            results = cursor.fetchall()
            
            posts = []
            for row in results:
                posts.append({
                    'id': row[0],
                    'post_id': row[1],
                    'subreddit': row[2],
                    'title': row[3],
                    'author': row[4],
                    'created_utc': row[5],
                    'score': row[6],
                    'upvote_ratio': row[7],
                    'url': row[8],
                    'permalink': row[9],
                    'num_comments': row[10],
                    'is_self': row[11],
                    'selftext': row[12],
                    'is_original_content': row[13],
                    'created_at': row[14],
                    'updated_at': row[15]
                })
            
            return posts
        except Exception as e:
            logger.error(f"获取热门帖子失败: {e}")
            return []
        finally:
            DatabasePool.close_connection(conn, cursor) 