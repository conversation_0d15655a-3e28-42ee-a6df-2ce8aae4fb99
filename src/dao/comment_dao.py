import os
import psycopg2
import datetime
from database.db_pool import DatabasePool
from utils.logger import get_logger

logger = get_logger('comment_dao')

class CommentDao:
    """Reddit评论数据访问对象"""
    
    @staticmethod
    def create_table_if_not_exists():
        """创建评论表（如果不存在）"""
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            # 创建评论表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS reddit_comments (
                id SERIAL PRIMARY KEY, -- 自增主键
                comment_id VARCHAR(20) NOT NULL UNIQUE, -- Reddit评论ID，唯一
                post_id VARCHAR(20) NOT NULL, -- 关联的帖子ID
                author VARCHAR(100), -- 评论作者
                body TEXT, -- 评论内容
                score INTEGER DEFAULT 0, -- 评论得分
                created_utc TIMESTAMP, -- 发布时间
                is_submitter BOOLEAN DEFAULT FALSE, -- 是否为原帖作者
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 记录更新时间
                FOREIGN KEY (post_id) REFERENCES reddit_posts(post_id) ON DELETE CASCADE -- 外键约束
            );
            
            -- 创建索引
            CREATE INDEX IF NOT EXISTS idx_comment_post_id ON reddit_comments(post_id);
            CREATE INDEX IF NOT EXISTS idx_comment_author ON reddit_comments(author);
            CREATE INDEX IF NOT EXISTS idx_comment_created ON reddit_comments(created_utc);
            CREATE INDEX IF NOT EXISTS idx_comment_score ON reddit_comments(score);
            
            -- 添加表注释
            COMMENT ON TABLE reddit_comments IS 'Reddit评论信息表';
            COMMENT ON COLUMN reddit_comments.id IS '主键ID';
            COMMENT ON COLUMN reddit_comments.comment_id IS 'Reddit评论ID';
            COMMENT ON COLUMN reddit_comments.post_id IS '关联的帖子ID';
            COMMENT ON COLUMN reddit_comments.author IS '评论作者';
            COMMENT ON COLUMN reddit_comments.body IS '评论内容';
            COMMENT ON COLUMN reddit_comments.score IS '评论得分';
            COMMENT ON COLUMN reddit_comments.created_utc IS '发布时间';
            COMMENT ON COLUMN reddit_comments.is_submitter IS '是否为原帖作者';
            COMMENT ON COLUMN reddit_comments.created_at IS '记录创建时间';
            COMMENT ON COLUMN reddit_comments.updated_at IS '记录更新时间';
            
            -- 更新时间触发器
            CREATE OR REPLACE FUNCTION update_comment_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ LANGUAGE 'plpgsql';
            
            -- 创建触发器
            DROP TRIGGER IF EXISTS update_comment_modtime ON reddit_comments;
            CREATE TRIGGER update_comment_modtime
                BEFORE UPDATE ON reddit_comments
                FOR EACH ROW
                EXECUTE FUNCTION update_comment_updated_at_column();
            """)
            
            conn.commit()
            logger.info("评论表创建/验证成功")
            return True
        except Exception as e:
            logger.error(f"创建评论表失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def insert_or_update_comment(comment_data, post_id):
        """
        插入或更新评论信息
        
        参数:
            comment_data: 评论数据字典
            post_id: 关联的帖子ID
        
        返回:
            插入/更新成功返回True，失败返回False
        """
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            # 检查评论是否已存在
            cursor.execute("""
            SELECT id FROM reddit_comments WHERE comment_id = %s
            """, (comment_data['id'],))
            
            result = cursor.fetchone()
            
            if result:
                # 更新现有评论
                cursor.execute("""
                UPDATE reddit_comments SET
                    author = %s,
                    body = %s,
                    score = %s,
                    is_submitter = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE comment_id = %s
                """, (
                    comment_data['author'],
                    comment_data['body'],
                    comment_data['score'],
                    comment_data.get('is_submitter', False),
                    comment_data['id']
                ))
                
                logger.debug(f"更新评论: {comment_data['id']}")
            else:
                # 插入新评论
                cursor.execute("""
                INSERT INTO reddit_comments (
                    comment_id, post_id, author, body, score, 
                    created_utc, is_submitter
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (
                    comment_data['id'],
                    post_id,
                    comment_data['author'],
                    comment_data['body'],
                    comment_data['score'],
                    comment_data.get('created_utc'),
                    comment_data.get('is_submitter', False)
                ))
                
                logger.info(f"插入新评论: {comment_data['id']}")
            
            conn.commit()
            return True
        except Exception as e:
            logger.error(f"插入/更新评论失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_comments_by_post_id(post_id, limit=50, offset=0, sort_by='created_utc', sort_order='DESC'):
        """
        获取指定帖子的评论列表
        
        参数:
            post_id: 帖子ID
            limit: 返回结果数量限制
            offset: 分页偏移量
            sort_by: 排序字段
            sort_order: 排序方向 ('ASC'或'DESC')
            
        返回:
            评论数据字典列表
        """
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            # 构建查询SQL
            query = f"""
            SELECT 
                id, comment_id, post_id, author, body, score,
                created_utc, is_submitter, created_at, updated_at
            FROM reddit_comments 
            WHERE post_id = %s
            ORDER BY {sort_by} {sort_order}
            LIMIT %s OFFSET %s
            """
            
            cursor.execute(query, (post_id, limit, offset))
            
            results = cursor.fetchall()
            
            comments = []
            for row in results:
                comments.append({
                    'id': row[0],
                    'comment_id': row[1],
                    'post_id': row[2],
                    'author': row[3],
                    'body': row[4],
                    'score': row[5],
                    'created_utc': row[6],
                    'is_submitter': row[7],
                    'created_at': row[8],
                    'updated_at': row[9]
                })
            
            return comments
        except Exception as e:
            logger.error(f"获取帖子评论列表失败: {e}")
            return []
        finally:
            DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_comments_by_author(author, limit=50, offset=0):
        """
        获取指定作者的评论列表
        
        参数:
            author: 作者用户名
            limit: 返回结果数量限制
            offset: 分页偏移量
            
        返回:
            评论数据字典列表
        """
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
            SELECT 
                id, comment_id, post_id, author, body, score,
                created_utc, is_submitter, created_at, updated_at
            FROM reddit_comments 
            WHERE author = %s
            ORDER BY created_utc DESC
            LIMIT %s OFFSET %s
            """, (author, limit, offset))
            
            results = cursor.fetchall()
            
            comments = []
            for row in results:
                comments.append({
                    'id': row[0],
                    'comment_id': row[1],
                    'post_id': row[2],
                    'author': row[3],
                    'body': row[4],
                    'score': row[5],
                    'created_utc': row[6],
                    'is_submitter': row[7],
                    'created_at': row[8],
                    'updated_at': row[9]
                })
            
            return comments
        except Exception as e:
            logger.error(f"获取作者评论列表失败: {e}")
            return []
        finally:
            DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_top_comments(limit=10, days=7):
        """
        获取一段时间内的热门评论
        
        参数:
            limit: 返回结果数量限制
            days: 时间范围（天）
            
        返回:
            热门评论列表
        """
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            # 计算日期范围
            date_threshold = datetime.datetime.now() - datetime.timedelta(days=days)
            
            cursor.execute("""
            SELECT 
                c.id, c.comment_id, c.post_id, c.author, c.body, c.score,
                c.created_utc, c.is_submitter, c.created_at, c.updated_at,
                p.title as post_title, p.subreddit
            FROM reddit_comments c
            JOIN reddit_posts p ON c.post_id = p.post_id
            WHERE c.created_utc > %s
            ORDER BY c.score DESC
            LIMIT %s
            """, (date_threshold, limit))
            
            results = cursor.fetchall()
            
            comments = []
            for row in results:
                comments.append({
                    'id': row[0],
                    'comment_id': row[1],
                    'post_id': row[2],
                    'author': row[3],
                    'body': row[4],
                    'score': row[5],
                    'created_utc': row[6],
                    'is_submitter': row[7],
                    'created_at': row[8],
                    'updated_at': row[9],
                    'post_title': row[10],
                    'subreddit': row[11]
                })
            
            return comments
        except Exception as e:
            logger.error(f"获取热门评论失败: {e}")
            return []
        finally:
            DatabasePool.close_connection(conn, cursor) 