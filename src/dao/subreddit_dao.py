import os
import psycopg2
import datetime
from database.db_pool import DatabasePool
from utils.logger import get_logger

logger = get_logger('subreddit_dao')

class SubredditDao:
    """Reddit子版块数据访问对象"""
    
    @staticmethod
    def create_table_if_not_exists():
        """创建子版块表（如果不存在）"""
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            # 创建子版块表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS reddit_subreddits (
                id SERIAL PRIMARY KEY, -- 自增主键
                name VARCHAR(100) NOT NULL UNIQUE, -- 子版块名称，唯一
                display_name VARCHAR(100), -- 子版块显示名称
                subscribers INTEGER DEFAULT 0, -- 订阅人数
                public_description TEXT, -- 公开描述
                created_utc TIMESTAMP, -- 创建时间
                rank INTEGER, -- 子版块排名
                url TEXT, -- 子版块URL地址
                last_crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 最后抓取时间
            );
            
            -- 创建索引
            CREATE INDEX IF NOT EXISTS idx_sub_name ON reddit_subreddits(name);
            CREATE INDEX IF NOT EXISTS idx_sub_rank ON reddit_subreddits(rank);
            
            -- 添加表注释
            COMMENT ON TABLE reddit_subreddits IS 'Reddit子版块信息表';
            COMMENT ON COLUMN reddit_subreddits.id IS '主键ID';
            COMMENT ON COLUMN reddit_subreddits.name IS '子版块名称';
            COMMENT ON COLUMN reddit_subreddits.display_name IS '显示名称';
            COMMENT ON COLUMN reddit_subreddits.subscribers IS '订阅人数';
            COMMENT ON COLUMN reddit_subreddits.public_description IS '公开描述';
            COMMENT ON COLUMN reddit_subreddits.created_utc IS '创建时间';
            COMMENT ON COLUMN reddit_subreddits.rank IS '子版块排名';
            COMMENT ON COLUMN reddit_subreddits.url IS '子版块URL地址';
            COMMENT ON COLUMN reddit_subreddits.last_crawled_at IS '最后抓取时间';
            """)
            
            conn.commit()
            logger.info("子版块表创建/验证成功")
            return True
        except Exception as e:
            logger.error(f"创建子版块表失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def insert_or_update_subreddit(subreddit_data):
        """
        插入或更新子版块信息
        
        参数:
            subreddit_data: 子版块数据字典
        
        返回:
            插入/更新成功返回子版块ID，失败返回None
        """
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            # 检查子版块是否已存在
            cursor.execute("""
            SELECT id FROM reddit_subreddits WHERE name = %s
            """, (subreddit_data['name'],))
            
            result = cursor.fetchone()
            
            if result:
                # 更新现有子版块
                subreddit_id = result[0]
                cursor.execute("""
                UPDATE reddit_subreddits SET
                    display_name = %s,
                    subscribers = %s,
                    public_description = %s,
                    rank = %s,
                    url = %s,
                    last_crawled_at = CURRENT_TIMESTAMP
                WHERE id = %s
                """, (
                    subreddit_data.get('display_name'),
                    subreddit_data.get('subscribers', 0),
                    subreddit_data.get('public_description', ''),
                    subreddit_data.get('rank'),
                    subreddit_data.get('url', ''),
                    subreddit_id
                ))
                
                logger.debug(f"更新子版块: {subreddit_data['name']}")
            else:
                # 插入新子版块
                cursor.execute("""
                INSERT INTO reddit_subreddits (
                    name, display_name, subscribers, 
                    public_description, created_utc, rank, url, last_crawled_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                RETURNING id
                """, (
                    subreddit_data['name'],
                    subreddit_data.get('display_name'),
                    subreddit_data.get('subscribers', 0),
                    subreddit_data.get('public_description', ''),
                    subreddit_data.get('created_utc'),
                    subreddit_data.get('rank'),
                    subreddit_data.get('url', '')
                ))
                
                subreddit_id = cursor.fetchone()[0]
                logger.info(f"插入新子版块: {subreddit_data['name']} (ID: {subreddit_id})")
            
            conn.commit()
            return subreddit_id
        except Exception as e:
            logger.error(f"插入/更新子版块失败: {e}")
            if conn:
                conn.rollback()
            return None
        finally:
            DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_subreddit_by_name(name):
        """
        通过名称获取子版块信息
        
        参数:
            name: 子版块名称
            
        返回:
            子版块数据字典，未找到则返回None
        """
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
            SELECT 
                id, name, display_name, subscribers, 
                public_description, created_utc, rank, url, last_crawled_at
            FROM reddit_subreddits 
            WHERE name = %s
            """, (name,))
            
            result = cursor.fetchone()
            
            if result:
                return {
                    'id': result[0],
                    'name': result[1],
                    'display_name': result[2],
                    'subscribers': result[3],
                    'public_description': result[4],
                    'created_utc': result[5],
                    'rank': result[6],
                    'url': result[7],
                    'last_crawled_at': result[8]
                }
            else:
                return None
        except Exception as e:
            logger.error(f"获取子版块信息失败: {e}")
            return None
        finally:
            DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_all_subreddits():
        """
        获取所有子版块信息
        
        返回:
            子版块数据字典列表
        """
        conn = None
        cursor = None
        try:
            conn = DatabasePool().get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
            SELECT 
                id, name, display_name, subscribers, 
                public_description, created_utc, rank, url, last_crawled_at
            FROM reddit_subreddits 
            ORDER BY rank ASC, subscribers DESC
            """)
            
            results = cursor.fetchall()
            
            subreddits = []
            for row in results:
                subreddits.append({
                    'id': row[0],
                    'name': row[1],
                    'display_name': row[2],
                    'subscribers': row[3],
                    'public_description': row[4],
                    'created_utc': row[5],
                    'rank': row[6],
                    'url': row[7],
                    'last_crawled_at': row[8]
                })
            
            return subreddits
        except Exception as e:
            logger.error(f"获取所有子版块信息失败: {e}")
            return []
        finally:
            DatabasePool.close_connection(conn, cursor) 