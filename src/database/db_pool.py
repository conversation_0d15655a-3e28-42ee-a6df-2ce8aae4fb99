import psycopg2
from psycopg2.pool import SimpleConnectionPool
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent.parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from dotenv import load_dotenv
from utils.logger import get_logger

# 加载环境变量
load_dotenv()

class DatabasePool:
    _instance = None
    _pool = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabasePool, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if DatabasePool._pool is None:
            self.log = get_logger('database')
            self.config = self._get_db_config()
            self._create_pool()
    
    def _get_db_config(self):
        """从环境变量获取数据库配置"""
        try:
            config = {
                'host': os.getenv('DB_HOST'),
                'port': int(os.getenv('DB_PORT', '5432')),
                'user': os.getenv('DB_USER'),
                'password': os.getenv('DB_PASSWORD'),
                'dbname': os.getenv('DB_NAME')  # PostgreSQL使用dbname而不是database
            }
            
            # 验证所有必需的配置
            missing = [k for k, v in config.items() if not v]
            if missing:
                raise ValueError(f"缺少数据库配置: {', '.join(missing)}")
            
            return config
        
        except Exception as e:
            self.log.error(f"数据库配置错误: {str(e)}")
            raise
    
    def _create_pool(self):
        """创建数据库连接池"""
        try:
            DatabasePool._pool = SimpleConnectionPool(
                minconn=int(os.getenv('DB_MIN_CONNECTIONS', '2')),
                maxconn=int(os.getenv('DB_MAX_CONNECTIONS', '10')),
                **self.config
            )
            self.log.debug("数据库连接池创建成功")
        except Exception as e:
            error_msg = f"创建数据库连接池失败: {str(e)}"
            self.log.error(error_msg)
            raise
    
    def get_connection(self):
        """获取数据库连接"""
        return DatabasePool._pool.getconn()

    def return_connection(self, conn):
        """归还连接到连接池"""
        DatabasePool._pool.putconn(conn)

    @staticmethod
    def close_connection(conn, cursor=None):
        """关闭数据库连接"""
        try:
            if cursor:
                cursor.close()
            if conn:
                DatabasePool._instance.return_connection(conn)
        except Exception as e:
            logger = get_logger('database')
            logger.error(f"关闭数据库连接失败: {str(e)}")

    def execute_query(self, sql, params=None):
        """执行查询并返回结果"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, params or ())
            return cursor.fetchall()
        finally:
            self.close_connection(conn, cursor)

    def execute_update(self, sql, params=None):
        """执行更新操作"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, params or ())
            conn.commit()
            return cursor.rowcount
        except Exception as e:
            if conn:
                conn.rollback()
            raise
        finally:
            self.close_connection(conn, cursor)

    def get_dict_cursor_connection(self):
        """获取返回字典结果的数据库连接"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        return conn, cursor 