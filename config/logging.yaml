version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  detailed:
    format: '%(asctime)s [%(levelname)s] %(name)s (%(filename)s:%(lineno)d): %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout

  file:
    class: logging.handlers.TimedRotatingFileHandler
    level: INFO
    formatter: detailed
    filename: logs/spider.log
    when: midnight
    backupCount: 30
    encoding: utf8

  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/error.log
    maxBytes: 10485760
    backupCount: 20
    encoding: utf8
    
  youtube_file:
    class: logging.handlers.TimedRotatingFileHandler
    level: INFO
    formatter: detailed
    filename: logs/youtube_spider.log
    when: midnight
    backupCount: 30
    encoding: utf8

loggers:
  '':  # 根日志记录器
    handlers: [console, file]
    level: INFO
    propagate: true

  main:
    handlers: [console, file, error_file]
    level: INFO
    propagate: false

  spider_ahhhf:
    handlers: [console, file, error_file]
    level: INFO
    propagate: false
    
  spider_youtube:
    handlers: [console, youtube_file, error_file]
    level: INFO
    propagate: false
    
  youtube_dao:
    handlers: [console, youtube_file, error_file]
    level: INFO
    propagate: false
    
  database:
    handlers: [console, file, error_file]
    level: INFO
    propagate: false

root:
  level: INFO
  handlers: [console, file] 